# 限时任务系统 - 第一阶段：系统初始化文档

## 📋 概述
第一阶段是限时任务系统的**启动和初始化阶段**，负责建立整个系统的基础架构，确保所有子模块正常工作并获取初始数据。

## 🎯 主要目标
- ✅ 初始化所有子模块实例
- ✅ 建立服务器通信连接
- ✅ 注册消息监听机制
- ✅ 获取任务基础数据
- ✅ 确保系统就绪状态

---

## 🔧 第一步：模块启动 (`TimeTaskModule.init()`)

### 📍 入口位置
```typescript
// 文件：assets/GameScrpit/module/time_task/TimeTaskModule.ts
// 方法：init(data?: any, completedCallback?: Function)
```

### 🔄 执行流程

#### **步骤1：清理旧资源**
```typescript
if (this._subscriber) {
  this._subscriber.unRegister();
}
```
**作用**：
- 🧹 清理之前可能存在的消息订阅
- 🔄 防止重复初始化造成的内存泄漏
- 🛡️ 确保系统重启时的干净状态

#### **步骤2：创建子模块实例**
```typescript
this._data = new TimeTaskData();           // 数据管理模块
this._api = new TimeTaskApi();             // 网络接口模块
this._service = new TimeTaskService();     // 业务逻辑模块
this._subscriber = new TimeTaskSubscriber(); // 消息订阅模块
this._route = new TimeTaskRoute();         // 路由管理模块
this._viewModel = new TimeTaskViewModel(); // 视图模型模块
this._config = new TimeTaskConfig();       // 配置管理模块
```
**作用**：
- 🏗️ 构建完整的模块架构
- 📦 每个模块负责特定功能领域
- 🔗 为后续功能调用提供基础

#### **步骤3：注册核心服务**
```typescript
// 注册消息订阅
this._subscriber.register();

// 初始化路由配置
this._route.init();
```
**作用**：
- 📡 建立与服务器的消息通道
- 🗺️ 配置UI界面路由规则
- 🔔 准备接收实时推送消息

#### **步骤4：获取初始数据**
```typescript
TimeTaskModule.api.timeTaskInfo(() => {
  completedCallback && completedCallback();
});
```
**作用**：
- 📊 从服务器获取任务列表和状态
- 🔄 同步最新的任务进度数据
- ✅ 通知调用方初始化完成

---

## 📡 第二步：消息订阅注册 (`TimeTaskSubscriber.register()`)

### 📍 位置
```typescript
// 文件：assets/GameScrpit/module/time_task/TimeTaskSubscriber.ts
// 方法：register()
```

### 🔔 订阅机制
```typescript
public register() {
  // 订阅服务器推送的任务进度更新消息
  ApiHandler.instance.subscribe(
    TimeTaskResponse,              // 消息类型
    ActivityCmd.TimeTaskNotice,    // 命令ID
    this.onTimeTaskNotice         // 处理函数
  );
}
```

### 📨 消息处理
```typescript
private onTimeTaskNotice(resp: TimeTaskResponse) {
  // 实时更新任务进度
  TimeTaskModule.data.updateTask(resp.taskId, resp.targetVal);
}
```

**核心价值**：
- ⚡ **实时性**：玩家完成任务后立即更新UI
- 🔄 **自动化**：无需手动刷新，系统自动同步
- 📊 **数据一致性**：确保客户端数据与服务器保持同步

---

## 🌐 第三步：网络数据获取 (`TimeTaskApi.timeTaskInfo()`)

### 📍 位置
```typescript
// 文件：assets/GameScrpit/module/time_task/TimeTaskApi.ts
// 方法：timeTaskInfo()
```

### 📡 请求流程
```typescript
timeTaskInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
  ApiHandler.instance.requestSync(
    TimeTaskMessage,                                    // 返回数据类型
    ActivityCmd.timeTaskInfo,                          // API命令
    LongValue.encode({ value: ActivityID.TIME_TASK_1 }), // 请求参数
    (data: TimeTaskMessage) => {
      // 保存到本地数据模块
      TimeTaskModule.data.timeTaskMessage = data;
      console.log(data);
    }
  );
}
```

### 📊 获取的数据内容
```typescript
interface TimeTaskMessage {
  activityId: number;                                    // 活动ID
  completeMap: { [key: number]: TaskCompleteMessage };  // 任务完成状态映射
}

interface TaskCompleteMessage {
  targetVal: number;    // 当前进度值
  takeList: number[];   // 已领取的奖励索引列表
}
```

**数据用途**：
- 📋 **任务列表**：显示所有可用任务
- 📊 **进度状态**：每个任务的完成进度
- 🎁 **奖励记录**：哪些奖励已经被领取
- ⏰ **活动信息**：当前活动的基本信息

---

## 🔄 初始化时序图

```mermaid
sequenceDiagram
    participant Game as 游戏主程序
    participant Module as TimeTaskModule
    participant Sub as TimeTaskSubscriber
    participant Api as TimeTaskApi
    participant Server as 服务器
    participant Data as TimeTaskData

    Game->>Module: 调用 init()
    Module->>Sub: unRegister() 清理旧订阅
    Module->>Module: 创建所有子模块实例
    Module->>Sub: register() 注册消息订阅
    Sub->>Server: 建立消息监听通道
    Module->>Api: timeTaskInfo() 获取数据
    Api->>Server: 请求任务信息
    Server->>Api: 返回 TimeTaskMessage
    Api->>Data: 保存数据到本地
    Module->>Game: 回调 completedCallback()
```

---

## ✅ 初始化完成标志

当第一阶段完成后，系统应该达到以下状态：

### 🟢 模块状态检查
- [ ] 所有子模块实例已创建
- [ ] 消息订阅已注册成功
- [ ] 路由配置已加载
- [ ] 服务器数据已获取并保存

### 📊 数据状态检查
- [ ] `TimeTaskModule.data.timeTaskMessage` 不为空
- [ ] `completeMap` 包含任务状态数据
- [ ] 任务进度和奖励状态正确加载

### 🔔 功能状态检查
- [ ] 能够接收服务器推送消息
- [ ] API接口调用正常
- [ ] 准备好为UI提供数据服务

---

## 🚨 常见问题与解决方案

### ❌ 问题1：初始化失败
**现象**：`completedCallback` 没有被调用
**原因**：网络请求失败或超时
**解决**：添加错误处理和重试机制

### ❌ 问题2：消息订阅无效
**现象**：任务进度不会自动更新
**原因**：`register()` 调用失败或时机不对
**解决**：确保在网络连接建立后再注册

### ❌ 问题3：数据为空
**现象**：`timeTaskMessage` 为 undefined
**原因**：服务器返回异常或解析失败
**解决**：添加数据验证和默认值处理

---

## 📝 开发注意事项

1. **🔄 重复初始化**：确保多次调用 `init()` 不会造成问题
2. **⏰ 时序依赖**：某些模块可能依赖其他模块的初始化结果
3. **🛡️ 异常处理**：网络异常时要有合理的降级策略
4. **📊 数据验证**：对服务器返回的数据进行完整性检查
5. **🔔 回调管理**：确保回调函数在正确的时机被调用

---

## 🎯 下一阶段预告

第一阶段完成后，系统将进入**第二阶段：数据管理与UI展示**，主要包括：
- 📊 数据结构的详细管理
- 🎨 UI界面的初始化和渲染
- 🔗 配置数据与运行时数据的结合
- 📋 任务列表的动态展示
