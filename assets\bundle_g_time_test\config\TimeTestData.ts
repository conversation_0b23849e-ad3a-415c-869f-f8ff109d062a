import { _decorator, Component, Node } from "cc";
import { TimeTaskMessage } from "../../GameScrpit/game/net/protocol/Activity";
const { ccclass, property } = _decorator;
/**
 *  限时测试任务数据
 */
@ccclass("TimeTestData")
export class TimeTestData extends Component {
  // 获取任务数据
  private _timeTestMessage: TimeTaskMessage;

  get timeTestMessage() {
    return this._timeTestMessage;
  }
  set timeTestMessage(value: TimeTaskMessage) {
    this._timeTestMessage = value;
  }

  updateTask(taskId: number, targetVal: number) {
    if (!this._timeTestMessage) {
      return;
    }
    if (!this._timeTestMessage.completeMap) {
      return;
    }
    if (!this._timeTestMessage.completeMap[taskId]) {
      return;
    }
  }

  update(deltaTime: number) {}
}
